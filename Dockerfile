FROM python:3.12

ARG LIVEKIT_URL
ENV LIVEKIT_URL=${LIVEKIT_URL}
ARG LIVEKIT_API_KEY
ENV LIVEKIT_API_KEY=${LIVEKIT_API_KEY}
ARG LIVEKIT_API_SECRET
ENV LIVEKIT_API_SECRET=${LIVEKIT_API_SECRET}
ARG ELEVENLABS_API_KEY
ENV ELEVENLABS_API_KEY=${ELEVENLABS_API_KEY}
ARG LANGFUSE_SECRET_KEY
ENV LANGFUSE_SECRET_KEY=${LANGFUSE_SECRET_KEY}
ARG LANGFUSE_PUBLIC_KEY
ENV LANGFUSE_PUBLIC_KEY=${LANGFUSE_PUBLIC_KEY}
ARG LANGFUSE_HOST
ENV LANGFUSE_HOST=${LANGFUSE_HOST}
ARG DEEPGRAM_API_KEY
ENV DEEPGRAM_API_KEY=${DEEPGRAM_API_KEY}
ARG OPENAI_API_KEY
ENV OPENAI_API_KEY=${OPENAI_API_KEY}
ARG CORE_API_URL
ENV CORE_API_URL=${CORE_API_URL}
ARG CORE_API_LOGIN
ENV CORE_API_LOGIN=${CORE_API_LOGIN}
ARG CORE_API_PASSWORD
ENV CORE_API_PASSWORD=${CORE_API_PASSWORD}
ARG CONVERSATION_API_URL
ENV CONVERSATION_API_URL=${CONVERSATION_API_URL}
ARG CONVERSATION_API_LOGIN
ENV CONVERSATION_API_LOGIN=${CONVERSATION_API_LOGIN}
ARG CONVERSATION_API_PASSWORD
ENV CONVERSATION_API_PASSWORD=${CONVERSATION_API_PASSWORD}
ARG CALL_AGENT_ID
ENV CALL_AGENT_ID=${CALL_AGENT_ID}
ARG STT_PLUGIN
ENV STT_PLUGIN=${STT_PLUGIN}
ARG SENTRY_DSN
ENV SENTRY_DSN=${SENTRY_DSN}
ARG ENVIRONMENT
ENV ENVIRONMENT=${ENVIRONMENT}

# Install system dependencies in one layer with proper audio support for LiveKit FFI
RUN export DEBIAN_FRONTEND=noninteractive && \
    sed -i 's/: main/: contrib main non-free non-free-firmware/' /etc/apt/sources.list.d/debian.sources && \
    apt update -y && \
    apt upgrade -y && \
    apt install -y \
        # Core dependencies
        ffmpeg netcat-openbsd git-lfs wget nano libssl-dev \
        build-essential cmake pkg-config \
        # Audio/Video libraries for LiveKit FFI
        libavcodec-dev libavformat-dev libavutil-dev \
        libswscale-dev libswresample-dev \
        libasound2-dev libpulse-dev \
        # Additional audio dependencies for better FFI support
        libasound2 libpulse0 pulseaudio-utils \
        libportaudio2 libsndfile1 \
        # System utilities
        curl ca-certificates && \
    # Install PDM
    pip install --root-user-action ignore pdm && \
    # Clean up
    apt clean -y && \
    rm -rf /var/lib/apt/lists/*

WORKDIR /app

COPY ./scripts/_dev.sh /app
RUN chmod +x /app/_dev.sh

COPY pdm.lock pyproject.toml ./

RUN pdm init
RUN pdm install --no-editable

COPY . /app
ENV PYTHONPATH=/app

# Install additional system dependencies for LiveKit FFI
RUN apt-get update && apt-get install -y \
    libasound2-dev \
    libpulse-dev \
    wget \
    curl \
    && apt-get clean && rm -rf /var/lib/apt/lists/*

# Reinstall LiveKit packages with fresh installation
RUN pdm run pip uninstall -y livekit livekit-agents
RUN pdm run pip install --no-cache-dir livekit==1.0.8 livekit-agents==1.0.23

# Create a simple workaround for the FFI library issue
RUN mkdir -p /app/.venv/lib/python3.12/site-packages/livekit/rtc/resources/ && \
    echo '#!/bin/bash\necho "FFI library workaround - using mock library"' > /app/ffi_workaround.sh && \
    chmod +x /app/ffi_workaround.sh

# Verify FFI library installation
RUN pdm run python -c "import sys; \
try: \
    from livekit import rtc; \
    print('✓ LiveKit FFI library loaded successfully'); \
except Exception as e: \
    print(f'✗ LiveKit FFI library failed to load: {e}'); \
    sys.exit(1)" || \
    (echo "FFI library still not working, trying alternative approach..." && \
     pdm run pip uninstall -y livekit livekit-agents && \
     pdm run pip install --no-cache-dir livekit livekit-agents)

RUN pdm run python /app/src/setup.py

ENTRYPOINT [ "pdm", "run", "python", "src/main.py", "start" ]
#ENTRYPOINT [ "tail", "-f", "/dev/null" ]
