# LiveKit FFI Library Solution

## Problem Overview

LiveKit uses a native FFI (Foreign Function Interface) library (`liblivekit_ffi.so`) for real-time audio/video processing. This library often causes issues in Docker containers due to:

1. **Architecture mismatch**: FFI library must match container CPU architecture
2. **Missing system dependencies**: Requires specific audio libraries
3. **Installation timing**: May fail during pip install
4. **Path resolution**: Library may not be found in expected locations

## Root Causes

### 1. Missing Audio System Dependencies
LiveKit FFI requires these system libraries:
- `libasound2` / `libasound2-dev` (ALSA audio)
- `libpulse0` / `libpulse-dev` (PulseAudio)
- `libportaudio2` (PortAudio)
- `libsndfile1` (Sound file handling)

### 2. Architecture Compatibility
The FFI library is compiled for specific architectures:
- x86_64 (Intel/AMD 64-bit)
- aarch64 (ARM 64-bit)
- Must match the container's architecture

### 3. Installation Order
Dependencies must be installed in the correct order:
1. System audio libraries first
2. Python dependencies
3. LiveKit packages
4. Plugin setup

## Reliable Solution

### 1. Dockerfile Improvements

```dockerfile
# Install comprehensive audio dependencies
RUN apt install -y \
    # Audio/Video libraries for LiveKit FFI
    libavcodec-dev libavformat-dev libavutil-dev \
    libswscale-dev libswresample-dev \
    libasound2-dev libpulse-dev \
    # Runtime audio libraries
    libasound2 libpulse0 pulseaudio-utils \
    libportaudio2 libsndfile1
```

### 2. Proper Installation Sequence

```dockerfile
# 1. Install system dependencies first
RUN apt install -y [audio-libraries]

# 2. Install Python package manager
RUN pip install pdm

# 3. Install Python dependencies
RUN pdm install --no-editable

# 4. Setup LiveKit plugins (downloads FFI if needed)
RUN pdm run python /app/src/setup.py

# 5. Verify installation
RUN pdm run python -c "from livekit import rtc; print('✓ FFI OK')"
```

### 3. Enhanced Setup Script

The `src/setup.py` now includes:
- Proper error handling for FFI library loading
- Detailed logging for troubleshooting
- Verification that FFI library works correctly
- Clear error messages for common issues

## Troubleshooting

### Common Error Messages

1. **"liblivekit_ffi.so: cannot open shared object file"**
   - Missing system audio dependencies
   - Install `libasound2`, `libpulse0`

2. **"Architecture mismatch"**
   - FFI library doesn't match container architecture
   - Ensure base image matches target deployment

3. **"Symbol not found"**
   - Incompatible audio library versions
   - Update system dependencies

### Verification Commands

```bash
# Check if FFI library exists
find /app -name "liblivekit_ffi.so" -type f

# Test LiveKit import
python -c "from livekit import rtc; print('OK')"

# Check system audio libraries
ldd $(find /app -name "liblivekit_ffi.so" | head -1)
```

## Best Practices

1. **Use official Python base images**: They have better compatibility
2. **Install audio dependencies early**: Before Python packages
3. **Use specific versions**: Pin LiveKit versions for consistency
4. **Test in CI/CD**: Verify FFI loading in automated builds
5. **Monitor logs**: Setup script provides detailed logging

## Alternative Approaches

If FFI issues persist, consider:

1. **Use LiveKit Cloud**: Avoid local FFI entirely
2. **Different base image**: Try Ubuntu or Alpine variants
3. **Multi-stage build**: Separate build and runtime environments
4. **Pre-built images**: Use LiveKit's official Docker images as base

## Migration from Old Workarounds

The previous approach used:
- Multiple reinstallations of LiveKit packages
- Mock FFI library creation
- Complex verification scripts

The new approach:
- Installs proper system dependencies upfront
- Uses single, clean installation
- Provides clear error messages
- Eliminates need for workarounds
