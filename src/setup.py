#!/usr/bin/env python3
"""
Setup script for LiveKit agent with proper FFI library handling.
This script downloads required plugin files and verifies LiveKit FFI library.
"""

import sys
import logging
import os

# Configure logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

def setup_livekit_plugins():
    """Download and setup LiveKit plugin files."""
    try:
        logger.info("Setting up LiveKit turn detector plugin...")
        from livekit.plugins import turn_detector

        eou_plugin = turn_detector.EOUPlugin()
        eou_plugin.download_files()
        logger.info("✓ Turn detector plugin setup completed")
        return True

    except Exception as e:
        logger.error(f"✗ Failed to setup turn detector plugin: {e}")
        return False

def verify_livekit_ffi():
    """Verify that LiveKit FFI library can be loaded."""
    try:
        logger.info("Verifying LiveKit FFI library...")

        # Try to import the core LiveKit RTC module
        from livekit import rtc
        logger.info("✓ LiveKit FFI library loaded successfully")

        # Try to create a basic room instance to ensure FFI is working
        room = rtc.Room()
        logger.info("✓ LiveKit Room creation successful - FFI is working properly")
        return True

    except ImportError as e:
        logger.error(f"✗ Failed to import LiveKit RTC module: {e}")
        return False
    except OSError as e:
        if "liblivekit_ffi" in str(e):
            logger.error(f"✗ LiveKit FFI library not found or incompatible: {e}")
            logger.error("This usually indicates missing system dependencies or architecture mismatch")
        else:
            logger.error(f"✗ System error loading LiveKit: {e}")
        return False
    except Exception as e:
        logger.error(f"✗ Unexpected error with LiveKit: {e}")
        return False

def main():
    """Main setup function."""
    logger.info("Starting LiveKit agent setup...")

    success = True

    # Setup plugins
    if not setup_livekit_plugins():
        success = False

    # Verify FFI library
    if not verify_livekit_ffi():
        success = False
        logger.error("FFI library verification failed. Check system dependencies:")
        logger.error("- Ensure audio libraries are installed (libasound2, libpulse0)")
        logger.error("- Verify architecture compatibility")
        logger.error("- Check that LiveKit version matches your system")

    if success:
        logger.info("✓ LiveKit agent setup completed successfully")
        sys.exit(0)
    else:
        logger.error("✗ LiveKit agent setup failed")
        sys.exit(1)

if __name__ == "__main__":
    main()
